// File: app/_layout.tsx

import React, { useEffect } from 'react';
import { Slot, useRouter, useSegments } from 'expo-router';
import { AuthProvider, useAuth } from '../hooks/useAuth'; // Adjust path to your useAuth hook
import { ActivityIndicator, View, StyleSheet } from 'react-native';
import * as SplashScreen from 'expo-splash-screen';

// Prevent the splash screen from auto-hiding while we determine auth state
// and load initial resources.
SplashScreen.preventAutoHideAsync();

/**
 * AppContentLayout handles the core redirection logic based on authentication status.
 */
function AppContentLayout() {
  const { user, loading: authLoading } = useAuth();
  const segments = useSegments(); // Gets the current URL segments, e.g., ['(tabs)', 'profile']
  const router = useRouter();

  useEffect(() => {
    // If authentication is still loading, don't do anything yet.
    // The splash screen (or a loading indicator) will be visible.
    if (authLoading) {
      return;
    }

    const isUserAuthenticated = !!user;
    // Check if the current route is the sign-in page.
    // Assumes your sign-in route is directly under app/, e.g., app/signin.tsx -> /signin
    const isOnSignInRoute = segments[0] === 'signin';

    // Check if the current route is part of the main authenticated app (inside the '(tabs)' group).
    const isInAppTabsGroup = segments[0] === '(tabs)';

    if (!isUserAuthenticated && !isOnSignInRoute) {
      // If the user is NOT authenticated AND they are NOT on the sign-in page,
      // redirect them to the sign-in page.
      router.replace('/signin');
    } else if (isUserAuthenticated && isOnSignInRoute) {
      // If the user IS authenticated AND they are somehow on the sign-in page
      // (e.g., after successful login before redirect, or by navigating back),
      // redirect them to the main app area (e.g., the first tab).
      router.replace('/(tabs)'); // This will navigate to the default screen in your (tabs) group.
    }

    // Once authentication state is known and user is on an appropriate route, hide the splash screen.
    if (!authLoading) {
      SplashScreen.hideAsync();
    }

  }, [user, authLoading, segments, router]);

  // While auth is loading, you can show a full-screen loader.
  // This is a fallback if SplashScreen.hideAsync() hasn't run yet or if auth takes a moment.
  if (authLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#007AFF" />
        {/* You could use your app's theme colors here */}
      </View>
    );
  }

  // <Slot /> renders the child route that matches the current URL.
  // This could be 'signin.tsx' or '(tabs)/_layout.tsx' (which then renders its own children).
  return <Slot />;
}

/**
 * RootLayout is the top-most layout for the application.
 * It wraps the entire app with the AuthProvider so that useAuth can be used anywhere.
 */
export default function RootLayout() {
  return (
    <AuthProvider>
      <AppContentLayout />
    </AuthProvider>
  );
}

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#121212', // Or your app's default background color
  },
});