// File: app/simulation/[id].tsx

import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, ActivityIndicator, TouchableOpacity, ScrollView, SafeAreaView, StatusBar } from 'react-native';
import { useLocalSearchParams, Stack, useRouter } from 'expo-router';
import { WebView } from 'react-native-webview'; // Import WebView
import { fetchSimulationById, SimulationDocument } from '../../services/firestoreService'; // Adjust path
import { Ionicons } from '@expo/vector-icons';

const HEADER_BACKGROUND_COLOR = '#1C1C1E'; // Consistent header color

export default function SimulationScreen() {
  const params = useLocalSearchParams<{ id: string }>(); // Get the 'id' param
  const router = useRouter();
  const simulationId = params.id;

  const [simulation, setSimulation] = useState<SimulationDocument | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isLiked, setIsLiked] = useState(false); // Placeholder for like state
  const [webViewLoading, setWebViewLoading] = useState(true); // For WebView's own loading

  useEffect(() => {
    if (simulationId) {
      setLoading(true);
      setError(null);
      fetchSimulationById(simulationId)
        .then(data => {
          if (data) {
            setSimulation(data);
          } else {
            setError('Simulation not found.');
          }
        })
        .catch(err => {
          console.error("Error fetching simulation details:", err);
          setError(err.message || 'Failed to load simulation details.');
        })
        .finally(() => {
          setLoading(false);
        });
    } else {
      setError("Simulation ID is missing.");
      setLoading(false);
    }
  }, [simulationId]);

  // Placeholder for like functionality
  const handleLikePress = () => {
    setIsLiked(!isLiked);
    console.log(isLiked ? 'Unliked' : 'Liked', simulationId);
    // TODO: Implement actual like/unlike logic with Firestore update
    // e.g., call a service function: likeSimulation(simulationId) or unlikeSimulation(simulationId)
  };

  if (loading) {
    return (
      <View style={styles.centeredMessageContainer}>
        <ActivityIndicator size="large" color="#FFFFFF" />
        <Text style={styles.loadingText}>Loading Simulation...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.centeredMessageContainer}>
        <Text style={styles.errorText}>{error}</Text>
        <TouchableOpacity onPress={() => router.back()} style={styles.retryButton}>
            <Text style={styles.retryButtonText}>Go Back</Text>
        </TouchableOpacity>
      </View>
    );
  }

  if (!simulation) {
    return (
      <View style={styles.centeredMessageContainer}>
        <Text style={styles.infoText}>Simulation data is unavailable.</Text>
      </View>
    );
  }

  // Assuming simulation.content contains the full HTML string
  const htmlContent = simulation.content;

  return (
    <SafeAreaView style={styles.safeArea}>
      <StatusBar barStyle="light-content" backgroundColor={HEADER_BACKGROUND_COLOR} />
      <Stack.Screen
        options={{
          title: simulation?.title || 'Simulation', // Dynamic title from fetched data
          headerStyle: { backgroundColor: HEADER_BACKGROUND_COLOR },
          headerTintColor: '#FFFFFF',
          headerTitleStyle: { fontWeight: 'bold', fontSize: 17 }, // Adjusted font size for potentially longer titles
          // Default back button is usually fine, but can be customized:
          // headerLeft: () => (
          //   <TouchableOpacity onPress={() => router.back()} style={{ marginLeft: 10 }}>
          //     <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
          //   </TouchableOpacity>
          // ),
          // headerRight: () => (
          //   <View style={styles.headerRightContainer}>
          //     <TouchableOpacity onPress={handleLikePress} style={styles.likeButton}>
          //       <Ionicons
          //         name={isLiked ? "heart" : "heart-outline"} // Optimistic UI for the icon
          //         size={24}
          //         color={isLiked ? "#FF6B6B" : "#FFFFFF"}
          //       />
          //     </TouchableOpacity>
          //     {/* Display the like count fetched from the server */}
          //     {simulation && simulation.likeCount !== undefined && (
          //       <Text style={styles.likeCountText}>{simulation.likeCount}</Text>
          //     )}
          //   </View>
          // ),
        }}
      />

      {/* Optional: Area for details above WebView, like description or the "SYSTEM::CUBE_CONFIG" bar */}
      {/* The green bar from your screenshot seems like metadata or config related to the simulation itself. */}
      {/* This could be part of the HTML content, or rendered natively if 'theme' or other fields dictate it. */}
      <View style={styles.metadataBar}>
        <Text style={styles.metadataText}>SYSTEM :: {simulation.theme || 'DEFAULT_CONFIG'}</Text>
        {/* A small animated bar or progress indicator could go here too */}
        <View style={styles.activityIndicatorSmall} />
      </View>

      <View style={styles.webViewContainer}>
        {webViewLoading && (
          <View style={styles.webViewLoader}>
            <ActivityIndicator size="large" color="#34D399" />
            <Text style={styles.webViewLoadingText}>Loading 3D Scene...</Text>
          </View>
        )}
        <WebView
          style={[styles.webView, webViewLoading && styles.webViewHidden]} // Hide WebView while its content loads
          originWhitelist={['*']} // Allows all origins. For production, restrict this if possible.
          source={{ html: htmlContent }}
          onLoadStart={() => setWebViewLoading(true)} // WebView content starts loading
          onLoadEnd={() => setWebViewLoading(false)}   // WebView content finished loading
          onError={(syntheticEvent) => {
            const { nativeEvent } = syntheticEvent;
            console.warn('WebView error: ', nativeEvent);
            setWebViewLoading(false);
            // Optionally show a specific error message for WebView failures
          }}
          // Other useful props:
          javaScriptEnabled={true}
          // domStorageEnabled={true}
          // startInLoadingState={true} // Shows a basic loading indicator from WebView itself
          // renderLoading={() => <ActivityIndicator size="large" />} // Custom loader for startInLoadingState
          scalesPageToFit={true} // Or false depending on your HTML content's responsiveness
          // mediaPlaybackRequiresUserAction={false} // If your sim has auto-playing audio/video
        />
      </View>

      {/* You could add other details below the WebView if needed, in a ScrollView */}
      {/* <ScrollView contentContainerStyle={styles.detailsContainer}>
        <Text style={styles.detailsTitle}>Description:</Text>
        <Text style={styles.detailsText}>{simulation.description}</Text>
        <Text style={styles.detailsTitle}>Author:</Text>
        <Text style={styles.detailsText}>{simulation.userName}</Text>
      </ScrollView> */}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#000000', // Screen background, likely black for a simulation
  },
  centeredMessageContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#121212',
  },
  loadingText: {
    marginTop: 10,
    color: '#A0A0A0',
    fontSize: 16,
  },
  infoText: {
    color: '#A0A0A0',
    fontSize: 16,
    textAlign: 'center',
  },
  errorText: {
    color: '#FF6B6B',
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 15,
  },
  retryButton: {
    backgroundColor: '#5A67D8',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  retryButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  headerRightContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 10,
  },
  likeButton: {
    padding: 5,
  },
  likeCountText: {
    color: '#FFFFFF',
    marginLeft: 5,
    fontSize: 14,
  },
  metadataBar: {
    backgroundColor: '#0A2A0A', // Dark green, almost black
    paddingHorizontal: 15,
    paddingVertical: 8,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: '#1A4A1A', // Slightly lighter green for border
  },
  metadataText: {
    color: '#34D399', // Bright green text
    fontSize: 13,
    fontFamily: 'monospace', // Give it a techy feel
    fontWeight: 'bold',
  },
  activityIndicatorSmall: { // Placeholder for the small green bar at the end
    width: 20,
    height: 6,
    backgroundColor: '#34D399',
    borderRadius: 3,
  },
  webViewContainer: {
    flex: 1, // WebView should take up most of the screen
    backgroundColor: '#000000', // Background for the WebView area
    position: 'relative', // For positioning the loader on top
  },
  webView: {
    flex: 1,
  },
  webViewHidden: {
    opacity: 0, // Hide WebView while its internal content is loading
  },
  webViewLoader: {
    ...StyleSheet.absoluteFillObject, // Cover the entire WebView container
    backgroundColor: 'rgba(0,0,0,0.7)', // Semi-transparent overlay
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1, // Ensure loader is on top
  },
  webViewLoadingText: {
    color: '#34D399',
    marginTop: 10,
    fontSize: 14,
  },
  // Optional details section styles
  // detailsContainer: {
  //   padding: 15,
  //   backgroundColor: '#121212',
  // },
  // detailsTitle: {
  //   color: '#FFFFFF',
  //   fontSize: 18,
  //   fontWeight: 'bold',
  //   marginTop: 10,
  // },
  // detailsText: {
  //   color: '#E0E0E0',
  //   fontSize: 15,
  //   lineHeight: 22,
  //   marginBottom: 10,
  // },
});