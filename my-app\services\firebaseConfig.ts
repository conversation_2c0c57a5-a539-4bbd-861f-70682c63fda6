// File: services/firebaseConfig.ts
import Constants from 'expo-constants';
import { initializeApp, getApp, getApps, FirebaseApp } from "firebase/app";
// Import getAuth for web, and other auth functions including getReactNativePersistence for native
import { initializeAuth, getAuth, Auth, getReactNativePersistence } from 'firebase/auth';
import ReactNativeAsyncStorage from '@react-native-async-storage/async-storage';
import { getFirestore, Firestore } from 'firebase/firestore';
import { Platform } from 'react-native'; // Import Platform

// Define a type for your firebase config if needed, for clarity
interface FirebaseConfig {
  apiKey?: string;
  authDomain?: string;
  projectId?: string;
  storageBucket?: string;
  messagingSenderId?: string;
  appId?: string;
}

const firebaseConfigFromConstants = Constants.expoConfig?.extra?.firebase as FirebaseConfig | undefined;

const firebaseConfig: FirebaseConfig = {
  apiKey: firebaseConfigFromConstants?.apiKey,
  authDomain: firebaseConfigFromConstants?.authDomain,
  projectId: firebaseConfigFromConstants?.projectId,
  storageBucket: firebaseConfigFromConstants?.storageBucket,
  messagingSenderId: firebaseConfigFromConstants?.messagingSenderId,
  appId: firebaseConfigFromConstants?.appId,
};

const googleClientIds = Constants.expoConfig?.extra?.google as { webClientId?: string; androidClientId?: string; iosClientId?: string } | undefined;
const webClientId: string | undefined = googleClientIds?.webClientId;
const androidClientId: string | undefined = googleClientIds?.androidClientId;
// const iosClientId: string | undefined = googleClientIds?.iosClientId; // If you add it

let app: FirebaseApp | undefined;
if (!getApps().length) {
  try {
    if (firebaseConfig.apiKey && firebaseConfig.projectId) {
      app = initializeApp(firebaseConfig);
    } else {
      console.error("Firebase config is missing or incomplete. App not initialized.");
    }
  } catch (error) {
    console.error("Firebase app initialization error:", error);
  }
} else {
  app = getApp();
}

let auth: Auth | undefined;
if (app) {
  try {
    if (Platform.OS === 'web') {
      // For web, use getAuth() which uses IndexedDB/localStorage by default.
      // Firebase v9+ web SDK initializes auth differently and getAuth is preferred after initializeApp.
      auth = getAuth(app);
      console.log("Firebase Auth initialized for Web using getAuth().");
    } else {
      // For native platforms (iOS/Android), use initializeAuth with ReactNativeAsyncStorage.
      // This requires @react-native-async-storage/async-storage.
      auth = initializeAuth(app, {
        persistence: getReactNativePersistence(ReactNativeAsyncStorage)
      });
      console.log("Firebase Auth initialized for Native with AsyncStorage persistence.");
    }
  } catch (error) {
    console.error("Firebase auth initialization error:", error);
    // Log a more specific warning if getReactNativePersistence is involved in an error on web
    // This shouldn't happen with the Platform.OS check, but it's a good diagnostic.
    if (Platform.OS === 'web' && error instanceof TypeError && (error as TypeError).message.includes('getReactNativePersistence')) {
        console.warn(
            "ExpoBuddy Warning: 'getReactNativePersistence' was unexpectedly called or caused an error on the web. " +
            "The Platform.OS check should prevent this. Consider using platform-specific files (e.g., .web.ts, .native.ts) for auth initialization if issues persist."
        );
        // As a fallback, try to initialize web auth if the native path was mistakenly taken.
        try {
            auth = getAuth(app);
            console.log("Firebase Auth (fallback) initialized for Web using getAuth().");
        } catch (fallbackError) {
            console.error("Firebase auth (fallback) initialization error for web:", fallbackError);
        }
    }
  }
} else {
  console.warn("Firebase app not initialized, auth will not be available.");
}

let firestore: Firestore | undefined;
if (app) {
  try {
    firestore = getFirestore(app);
    console.log("Firestore initialized successfully.");
  } catch (error) {
    console.error("Firestore initialization error:", error);
  }
} else {
  console.warn("Firebase app not initialized, firestore will not be available.");
}

export { app, auth, firestore, webClientId, androidClientId };