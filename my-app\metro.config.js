// metro.config.js
const { getDefaultConfig } = require('@expo/metro-config');

const config = getDefaultConfig(__dirname);

// Add 'cjs' to sourceExts if it's not already there.
// Firebase JS SDK uses .cjs files.
if (!config.resolver.sourceExts.includes('cjs')) {
  config.resolver.sourceExts.push('cjs');
}

// According to your research and some community findings for Firebase v9+,
// disabling unstable_enablePackageExports can help Metro bundle Firebase correctly.
config.resolver.unstable_enablePackageExports = false;

module.exports = config;