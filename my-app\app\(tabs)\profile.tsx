// File: app/(tabs)/profile.tsx
import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  TextInput,
  ActivityIndicator,
  ScrollView,
  Alert,
} from 'react-native';
import { Stack } from 'expo-router';
import { Ionicons, MaterialIcons } from '@expo/vector-icons';
import { useAuth } from '../../hooks/useAuth'; // Adjust path
import { fetchUserProfile, updateUserProfile, UserProfileDocument } from '../../services/firestoreService'; // Adjust path
import SimulationCard, { Simulation as SimulationCardData } from '../../components/SimulationCard'; // For "My Creations/Likes"
// You'll need a function to fetch user's creations/likes similar to fetchSimulations
// import { fetchUserCreations, fetchUserLikedSimulations } from '../../services/firestoreService';

const PROFILE_PLACEHOLDER_COLOR = '#5A67D8'; // Or your app's accent
const SCREEN_BACKGROUND_COLOR = '#121212';
const CARD_BACKGROUND_COLOR = '#1C1C1E';
const INPUT_BACKGROUND_COLOR = '#2C2C2E';
const TEXT_COLOR_PRIMARY = '#FFFFFF';
const TEXT_COLOR_SECONDARY = '#A0A0A0';
const ACCENT_COLOR = '#5A67D8';
const DESTRUCTIVE_COLOR = '#FF6B6B';

type ActiveContent = 'creations' | 'likes';

export default function ProfileScreen() {
  const { user, signOut, loading: authLoading } = useAuth();
  const [profileData, setProfileData] = useState<UserProfileDocument | null>(null);
  const [bio, setBio] = useState('');
  const [displayName, setDisplayName] = useState('');
  const [isEditing, setIsEditing] = useState(false);
  const [loadingProfile, setLoadingProfile] = useState(true);
  const [savingProfile, setSavingProfile] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const [activeContent, setActiveContent] = useState<ActiveContent>('creations');
  // Placeholder for creations/likes data - to be implemented
  // const [creations, setCreations] = useState<SimulationCardData[]>([]);
  // const [likes, setLikes] = useState<SimulationCardData[]>([]);
  // const [loadingContent, setLoadingContent] = useState(false);

  // Task 5.1.4: Fetch user information
  useEffect(() => {
    if (user?.uid) {
      setLoadingProfile(true);
      setError(null);
      fetchUserProfile(user.uid)
        .then(data => {
          if (data) {
            setProfileData(data);
            setBio(data.bio || '');
            setDisplayName(data.displayName || user.displayName || ''); // Prioritize Firestore displayName
          } else {
            // No profile in Firestore yet, use Auth data as fallback
            setDisplayName(user.displayName || '');
            setBio(''); // No bio yet
            // Optionally create a basic profile document here if desired
          }
        })
        .catch(err => {
          console.error("Error fetching user profile:", err);
          setError("Could not load profile information.");
        })
        .finally(() => setLoadingProfile(false));
    } else if (!authLoading) { // If user is null and auth is not loading
      setLoadingProfile(false);
      // Handle case where user is somehow null (should be caught by root layout)
    }
  }, [user, authLoading]);

  const handleEditToggle = () => {
    if (isEditing) { // If was editing, reset fields to original on cancel
      setDisplayName(profileData?.displayName || user?.displayName || '');
      setBio(profileData?.bio || '');
    }
    setIsEditing(!isEditing);
  };

  const handleSaveProfile = async () => {
    if (!user?.uid) return;
    if (!displayName.trim()) {
        Alert.alert("Display Name Required", "Please enter a display name.");
        return;
    }

    setSavingProfile(true);
    setError(null);
    try {
      const dataToUpdate: Partial<UserProfileDocument> = {
        displayName: displayName.trim(),
        bio: bio.trim(),
        // If you allow photoURL update, include it here
      };
      await updateUserProfile(user.uid, dataToUpdate);
      // Optimistically update local state or re-fetch
      setProfileData(prev => ({
        ...(prev || { uid: user.uid, email: user.email } as UserProfileDocument), // Base if prev is null
        displayName: displayName.trim(),
        bio: bio.trim(),
      }));
      setIsEditing(false);
      Alert.alert("Profile Saved", "Your profile has been updated successfully.");
    } catch (err: any) {
      console.error("Error saving profile:", err);
      setError(err.message || "Failed to save profile.");
      Alert.alert("Error", "Could not save your profile. Please try again.");
    } finally {
      setSavingProfile(false);
    }
  };

  // Placeholder for loading user's creations/likes
  // useEffect(() => {
  //   if (user?.uid && activeContent) {
  //     setLoadingContent(true);
  //     const fetchFn = activeContent === 'creations' ? fetchUserCreations : fetchUserLikedSimulations;
  //     fetchFn(user.uid)
  //       .then(data => activeContent === 'creations' ? setCreations(data) : setLikes(data))
  //       .catch(err => console.error(`Error fetching ${activeContent}:`, err))
  //       .finally(() => setLoadingContent(false));
  //   }
  // }, [user, activeContent]);


  if (authLoading || loadingProfile) {
    return (
      <View style={styles.centeredMessageContainer}>
        <ActivityIndicator size="large" color={TEXT_COLOR_PRIMARY} />
      </View>
    );
  }

  if (!user) {
    // This case should ideally be handled by the root layout redirecting to sign-in
    return (
      <View style={styles.centeredMessageContainer}>
        <Text style={styles.errorText}>Not authenticated. Please sign in.</Text>
      </View>
    );
  }

  return (
    <SafeAreaView style={styles.safeArea}>
      <StatusBar barStyle="light-content" backgroundColor={SCREEN_BACKGROUND_COLOR} />
      <Stack.Screen options={{ 
        title: 'Profile', 
        headerStyle: { backgroundColor: SCREEN_BACKGROUND_COLOR }, // Or your desired header color for tabs
        headerTintColor: TEXT_COLOR_PRIMARY,
        headerTitleStyle: { fontWeight: 'bold' },
        headerShadowVisible: false, 
        }} />

      <ScrollView contentContainerStyle={styles.scrollContainer}>
        {/* Profile Info Section (Task 5.1.1 & 5.1.4) */}
        <View style={styles.profileHeader}>
          <View style={styles.avatarPlaceholder}>
            <Ionicons name="person" size={60} color={TEXT_COLOR_PRIMARY} />
            {/* Replace with <Image source={{ uri: profileData?.photoURL || user?.photoURL }} /> if available */}
          </View>
          <Text style={styles.displayName}>{displayName || "User"}</Text>
          <Text style={styles.emailText}>{user.email}</Text>
        </View>

        {!isEditing ? (
          <>
            <View style={styles.bioSection}>
              <Text style={styles.sectionTitle}>Bio:</Text>
              <Text style={styles.bioText}>{bio || 'No bio yet. Add something about yourself!'}</Text>
            </View>
            <TouchableOpacity style={styles.buttonPrimary} onPress={handleEditToggle}>
              <Text style={styles.buttonTextPrimary}>Edit Profile</Text>
            </TouchableOpacity>
          </>
        ) : (
          // Editing Mode UI (Task 5.1.2)
          <View style={styles.editSection}>
            <Text style={styles.inputLabel}>Display Name</Text>
            <TextInput
              style={styles.input}
              value={displayName}
              onChangeText={setDisplayName}
              placeholder="Your Display Name"
              placeholderTextColor={TEXT_COLOR_SECONDARY}
            />
            <Text style={styles.inputLabel}>Bio</Text>
            <TextInput
              style={[styles.input, styles.bioInput]}
              value={bio}
              onChangeText={setBio}
              placeholder="Tell us about yourself..."
              placeholderTextColor={TEXT_COLOR_SECONDARY}
              multiline
              numberOfLines={4}
            />
            <View style={styles.editButtonsContainer}>
              <TouchableOpacity style={[styles.buttonSecondary, styles.cancelButton]} onPress={handleEditToggle} disabled={savingProfile}>
                <Text style={styles.buttonTextSecondary}>Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity style={[styles.buttonPrimary, styles.saveButton]} onPress={handleSaveProfile} disabled={savingProfile}>
                {savingProfile ? <ActivityIndicator color={TEXT_COLOR_PRIMARY} size="small"/> : <Text style={styles.buttonTextPrimary}>Save</Text>}
              </TouchableOpacity>
            </View>
          </View>
        )}

        {error && <Text style={styles.errorTextGlobal}>{error}</Text>}

        {/* Tabs for Creations/Likes (Task 5.1.3) */}
        <View style={styles.tabsContainer}>
          <TouchableOpacity
            style={[styles.tabButton, activeContent === 'creations' && styles.activeTabButton]}
            onPress={() => setActiveContent('creations')}
          >
            <Text style={[styles.tabText, activeContent === 'creations' && styles.activeTabText]}>My Creations</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.tabButton, activeContent === 'likes' && styles.activeTabButton]}
            onPress={() => setActiveContent('likes')}
          >
            <Text style={[styles.tabText, activeContent === 'likes' && styles.activeTabText]}>My Likes</Text>
          </TouchableOpacity>
        </View>

        {/* Content Area for Creations/Likes */}
        <View style={styles.contentListArea}>
          {/* {loadingContent ? (
            <ActivityIndicator color={TEXT_COLOR_PRIMARY} style={{marginTop: 20}}/>
          ) : (activeContent === 'creations' && creations.length > 0) ? (
            creations.map(sim => <SimulationCard key={sim.id} simulation={sim} />)
          ) : (activeContent === 'likes' && likes.length > 0) ? (
            likes.map(sim => <SimulationCard key={sim.id} simulation={sim} />)
          ) : (
            <Text style={styles.emptyContentText}>
              {activeContent === 'creations' ? 'No creations yet.' : 'You haven’t liked any simulations yet.'}
            </Text>
          )} */}
          {/* Placeholder - replace with actual FlatList for creations/likes */}
           <Text style={styles.emptyContentText}>
             {activeContent === 'creations' ? 'My Creations list will go here.' : 'My Likes list will go here.'}
           </Text>
           {/* Example of one card for styling reference */}
           {activeContent === 'creations' && (
                <SimulationCard simulation={{id: 'temp1', title: 'Matrix Digital Rain Number One', description:'A 3D visualization of a pixelated number one with cascading green code in a Matrix-style.', authorName: displayName || "Me", views: '1.2k', forks: 0, likes:0, iconPlaceholderColor: ACCENT_COLOR }}/>
           )}
        </View>


        {/* Sign Out Button (Task 5.1.1) */}
        <TouchableOpacity style={styles.buttonDestructive} onPress={signOut}>
          <Text style={styles.buttonTextPrimary}>Sign Out</Text>
        </TouchableOpacity>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: SCREEN_BACKGROUND_COLOR,
  },
  scrollContainer: {
    paddingBottom: 40, // Space for sign out button and potential tab bar overlap
  },
  centeredMessageContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: SCREEN_BACKGROUND_COLOR,
  },
  profileHeader: {
    alignItems: 'center',
    paddingVertical: 30,
    backgroundColor: CARD_BACKGROUND_COLOR, // Darker card section
    marginBottom: 20,
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
  },
  avatarPlaceholder: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: PROFILE_PLACEHOLDER_COLOR,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 15,
  },
  displayName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: TEXT_COLOR_PRIMARY,
    marginBottom: 4,
  },
  emailText: {
    fontSize: 15,
    color: TEXT_COLOR_SECONDARY,
    marginBottom: 20,
  },
  bioSection: {
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: TEXT_COLOR_SECONDARY,
    marginBottom: 6,
    textTransform: 'uppercase',
  },
  bioText: {
    fontSize: 15,
    color: TEXT_COLOR_PRIMARY,
    lineHeight: 22,
  },
  buttonPrimary: {
    backgroundColor: ACCENT_COLOR,
    paddingVertical: 15,
    marginHorizontal: 20,
    borderRadius: 12,
    alignItems: 'center',
    marginBottom: 20,
  },
  buttonTextPrimary: {
    color: TEXT_COLOR_PRIMARY,
    fontSize: 16,
    fontWeight: '600',
  },
  buttonSecondary: {
    borderColor: ACCENT_COLOR,
    borderWidth: 1,
    paddingVertical: 15,
    borderRadius: 12,
    alignItems: 'center',
  },
  buttonTextSecondary: {
    color: ACCENT_COLOR,
    fontSize: 16,
    fontWeight: '600',
  },
  buttonDestructive: {
    backgroundColor: DESTRUCTIVE_COLOR,
    paddingVertical: 15,
    marginHorizontal: 20,
    borderRadius: 12,
    alignItems: 'center',
    marginTop: 30,
  },
  // Editing Styles
  editSection: {
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 14,
    color: TEXT_COLOR_SECONDARY,
    marginBottom: 6,
    marginTop: 10,
  },
  input: {
    backgroundColor: INPUT_BACKGROUND_COLOR,
    color: TEXT_COLOR_PRIMARY,
    paddingHorizontal: 15,
    paddingVertical: 12,
    borderRadius: 8,
    fontSize: 15,
    marginBottom: 15,
    borderWidth: 1,
    borderColor: '#404040'
  },
  bioInput: {
    minHeight: 100,
    textAlignVertical: 'top',
  },
  editButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 10,
  },
  cancelButton: {
    flex: 1,
    marginRight: 10,
    borderColor: TEXT_COLOR_SECONDARY,
  },
  saveButton: {
    flex: 1,
    marginLeft: 10,
  },
  errorTextGlobal: {
      color: DESTRUCTIVE_COLOR,
      textAlign: 'center',
      marginHorizontal: 20,
      marginBottom: 15,
  },
  // Tabs for Creations/Likes
  tabsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    backgroundColor: CARD_BACKGROUND_COLOR,
    marginHorizontal: 20,
    borderRadius: 12,
    overflow: 'hidden', // To clip borderRadius for children
    marginBottom: 20,
  },
  tabButton: {
    flex: 1,
    paddingVertical: 15,
    alignItems: 'center',
    borderBottomWidth: 3,
    borderBottomColor: 'transparent',
  },
  activeTabButton: {
    borderBottomColor: ACCENT_COLOR,
  },
  tabText: {
    color: TEXT_COLOR_SECONDARY,
    fontSize: 15,
    fontWeight: '500',
  },
  activeTabText: {
    color: TEXT_COLOR_PRIMARY,
    fontWeight: '700',
  },
  contentListArea: {
    paddingHorizontal: 10, // Cards have their own horizontal margin
    minHeight: 150, // So empty message is visible
  },
  emptyContentText: {
    textAlign: 'center',
    color: TEXT_COLOR_SECONDARY,
    marginTop: 30,
    fontSize: 15,
  },
  // From AuthLoading or Profile Loading
  errorText: { // Renamed from styles.errorText in other files to avoid conflict if merged
    color: DESTRUCTIVE_COLOR,
    fontSize: 16,
    textAlign: 'center',
  },
});