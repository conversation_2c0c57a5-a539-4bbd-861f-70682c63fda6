// File: app.config.js
require('dotenv').config(); // Load environment variables from .env for local development

export default ({ config }) => {
  // config parameter contains the default generated app.json config
  // You can modify it or return a new object.

  // --- App Specific Identifiers ---
  const appName = 'Effect'; //  UPDATE THIS: Your app's display name
  const appSlug = 'effect'; //  UPDATE THIS: Your app's slug (URL-friendly name)
  const appScheme = 'com.danjeey.effect'; //  UPDATE THIS: A unique scheme for deep linking
  const iosBundleIdentifier = 'com.danjeey.effect'; //  UPDATE THIS: Your iOS bundle ID
  const androidPackage = 'com.danjeey.effect'; //  UPDATE THIS: Your Android package name

  return {
    // Spread the existing static config from app.json if it was generated
    // or use values directly as you were in your provided app.json
    ...config, // This line is important if Expo CLI generates a base config in memory

    // --- Basic App Info (from your provided config or update as needed) ---
    name: appName,
    slug: appSlug,
    owner: 'jjumpman00',
    version: '1.0.0',
    orientation: 'portrait',
    icon: './assets/images/icon.png', // Ensure this path is correct
    scheme: appScheme,
    userInterfaceStyle: 'automatic', // Or 'light', 'dark'

    // --- Platform Specific Configurations ---
    ios: {
      supportsTablet: true,
      bundleIdentifier: iosBundleIdentifier,
      // For Google Sign-In on standalone iOS, add the REVERSED client ID from your GoogleService-Info.plist
      // This is obtained when you add your iOS app to your Firebase project.
      infoPlist: {
        CFBundleURLTypes: [
          {
            // Ensure process.env.GOOGLE_IOS_REVERSED_CLIENT_ID is set in your .env / EAS Secrets
            CFBundleURLSchemes: [process.env.GOOGLE_IOS_REVERSED_CLIENT_ID || 'YOUR_FALLBACK_IOS_REVERSED_CLIENT_ID'],
          },
        ],
      },
    },
    android: {
      adaptiveIcon: {
        foregroundImage: './assets/images/adaptive-icon.png', // Ensure path is correct
        backgroundColor: '#FFFFFF', // Match your app's theme
      },
      package: androidPackage,
      // edgeToEdgeEnabled: true, // Retained from your input, ensure you handle safe areas
    },
    web: {
      bundler: 'metro',
      output: 'static', // Retained
      favicon: './assets/images/favicon.png', // Ensure path is correct
    },

    // --- Plugins (ensure these are installed in package.json) ---
    plugins: [
      'expo-router', // For file-system based routing
      [
        'expo-splash-screen', // For custom splash screen
        {
          image: './assets/images/splash-icon.png', // UPDATE if needed
          imageWidth: 200,
          resizeMode: 'contain',
          backgroundColor: '#FFFFFF', // Match your app's theme
        },
      ],
      'expo-web-browser', // For opening web links, used by expo-auth-session
      // Add other plugins as needed
    ],

    // --- Experiments (from your provided config) ---
    experiments: {
      typedRoutes: true, // For Expo Router typed routes
    },

    // --- Expo Application Services (EAS) ---
    // eas: { // You might have an eas.json for this, but projectId can also be here
    //   projectId: process.env.EAS_PROJECT_ID || "YOUR_EAS_PROJECT_ID_FALLBACK",
    // },

    // --- Custom Data Accessible at Runtime via Constants.expoConfig.extra ---
    extra: {
      // Firebase Configuration (loaded from environment variables)
      firebase: {
        apiKey: process.env.FIREBASE_API_KEY,
        authDomain: process.env.FIREBASE_AUTH_DOMAIN,
        projectId: process.env.FIREBASE_PROJECT_ID,
        storageBucket: process.env.FIREBASE_STORAGE_BUCKET,
        messagingSenderId: process.env.FIREBASE_MESSAGING_SENDER_ID,
        appId: process.env.FIREBASE_APP_ID,
        // measurementId: process.env.FIREBASE_MEASUREMENT_ID, // Optional
      },
      // Google Client IDs (loaded from environment variables)
      google: {
        // expoClientId: process.env.GOOGLE_EXPO_CLIENT_ID, // For Expo Go / Web
        // iosClientId: process.env.GOOGLE_IOS_CLIENT_ID,   // For standalone iOS
        androidClientId: process.env.GOOGLE_ANDROID_CLIENT_ID, // For standalone Android
        webClientId: process.env.GOOGLE_WEB_CLIENT_ID, // For web
      },
      // EAS Project ID (useful to have in constants if needed)
      eas: {
        projectId: process.env.EAS_PROJECT_ID
      },
      // You can add any other custom config your app might need
      // myCustomValue: "hello world",
    },

    // --- Deprecated / To Review from your input ---
    // "newArchEnabled": true, // This is for React Native's New Architecture.
                              // While supported by Expo SDK 49+, ensure all your native
                              // dependencies also support it. It's generally for advanced use cases.
                              // If you encounter issues, you might consider setting this to false
                              // unless specifically needed and tested.
    // "edgeToEdgeEnabled": true, // for Android. Ensure you handle insets/safe areas correctly in your UI.
  };
};