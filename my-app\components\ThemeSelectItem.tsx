// File: components/ThemeSelectItem.tsx
import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons'; // Or your preferred icon set

export interface Theme {
  id: string;
  name: string;
  iconName?: keyof typeof Ionicons.glyphMap; // Optional: if you use vector icons
  // iconUri?: string; // Optional: if you have image URIs for themes
}

interface ThemeSelectItemProps {
  theme: Theme;
  isSelected: boolean;
  onPress: () => void;
}

const ThemeSelectItem: React.FC<ThemeSelectItemProps> = ({ theme, isSelected, onPress }) => {
  return (
    <TouchableOpacity
      style={[styles.container, isSelected && styles.selectedContainer]}
      onPress={onPress}
    >
      <View style={[styles.iconPlaceholder, isSelected && styles.selectedIconPlaceholder]}>
        {/* Placeholder "not allowed" icon from screenshot, or use theme.iconName */}
        <Ionicons
          name={theme.iconName || "ban-outline"} // Default to 'ban-outline' if no specific icon
          size={30}
          color={isSelected ? '#FFFFFF' : '#A0A0A0'}
        />
      </View>
      <Text style={[styles.name, isSelected && styles.selectedName]}>{theme.name}</Text>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    width: 100, // Adjust as needed
    height: 100, // Make it square or adjust
    borderRadius: 12,
    backgroundColor: '#2C2C2E', // Dark item background
    padding: 10,
    marginRight: 12,
    alignItems: 'center',
    justifyContent: 'center', // Center content
    borderWidth: 2,
    borderColor: 'transparent', // Default no border
  },
  selectedContainer: {
    borderColor: '#5A67D8', // Accent color border when selected
    backgroundColor: '#3A3A4A', // Slightly different background when selected
  },
  iconPlaceholder: {
    width: 50,
    height: 50,
    borderRadius: 8,
    backgroundColor: '#48484A', // Darker placeholder
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  selectedIconPlaceholder: {
    backgroundColor: '#5A67D8', // Accent color when selected
  },
  name: {
    color: '#E0E0E0',
    fontSize: 13,
    textAlign: 'center',
  },
  selectedName: {
    color: '#FFFFFF',
    fontWeight: '600',
  },
});

export default ThemeSelectItem;