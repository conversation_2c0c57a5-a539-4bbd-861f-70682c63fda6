import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet, StatusBar, ActivityIndicator, Alert } from 'react-native';
import { useAuth } from '../hooks/useAuth'; // Adjust path if needed
import { useRouter } from 'expo-router';

export default function SignInScreen() {
  const { signInWithGoogle, loading, error, user } = useAuth();
  const router = useRouter();

  // This useEffect is to navigate away if user becomes authenticated
  // while on this screen (e.g. restored session or successful login)
  React.useEffect(() => {
    if (user) {
      router.replace('/(tabs)'); // Navigate to your main authenticated screen
    }
  }, [user, router]);

  const handleSignIn = async () => {
    try {
      await signInWithGoogle();
      // Navigation will be handled by the useEffect in _layout.tsx or the one above
    } catch (e) {
      // Error is already handled and set in useAuth, but you can log or show specific UI here
      console.error("Sign in attempt failed on screen:", e);
      Alert.alert("Sign-In Error", "Could not sign in with Google. Please try again.");
    }
  };

  // Since your UI has "Sign In" and "Sign Up" buttons that both use Google,
  // they will likely trigger the same Google Sign-In flow.
  // Firebase handles whether it's a new user (sign up) or existing (sign in)
  // automatically when using GoogleAuthProvider.

  if (loading && !error) { // Show loading indicator only if not also showing an error
    return (
      <View style={styles.container}>
        <ActivityIndicator size="large" color="#FFFFFF" />
        <Text style={styles.loadingText}>Signing in...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" />
      <Text style={styles.title}>EFFECT</Text>
      <Text style={styles.subtitle}>Sign in or create an account</Text>

      {error && (
        <Text style={styles.errorText}>
          Error: {error.message || 'An unexpected error occurred.'}
        </Text>
      )}

      <TouchableOpacity
        style={styles.button}
        onPress={handleSignIn}
        disabled={loading} // Disable button while loading
      >
        <Text style={styles.buttonText}>Sign In with Google</Text>
      </TouchableOpacity>

      {/* The "Sign Up with Google" button will trigger the same handleSignIn function
          as Firebase's Google provider handles new vs. existing users seamlessly.
          If you need distinct logic, you'd create a separate handler. */}
      <TouchableOpacity
        style={[styles.button, styles.signUpButton]}
        onPress={handleSignIn} // Same handler
        disabled={loading}
      >
        <Text style={styles.buttonText}>Sign Up with Google</Text>
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#121212',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 20,
  },
  title: {
    fontSize: 48,
    fontWeight: 'bold',
    color: '#FFFFFF',
    textAlign: 'center',
    marginBottom: 10,
  },
  subtitle: {
    fontSize: 16,
    color: '#A0A0A0',
    textAlign: 'center',
    marginBottom: 40,
  },
  button: {
    backgroundColor: '#5A67D8',
    paddingVertical: 15,
    paddingHorizontal: 30,
    borderRadius: 25,
    width: '80%',
    alignItems: 'center',
    marginBottom: 20,
    boxShadow: '0px 2px 3.84px rgba(0, 0, 0, 0.25)'
  },
  signUpButton: {
    // No different styles for now
  },
  buttonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  loadingText: {
    marginTop: 10,
    color: '#FFFFFF',
  },
  errorText: {
    color: 'red',
    marginBottom: 20,
    textAlign: 'center',
    maxWidth: '80%',
  }
});