// File: hooks/useAuth.tsx
import React, {
  createContext,
  useContext,
  useEffect,
  useState,
  ReactNode,
} from 'react';
import { User, onAuthStateChanged, signOut as firebaseSignOut } from 'firebase/auth';
import { GoogleAuthProvider, signInWithCredential } from 'firebase/auth';
import * as Google from 'expo-auth-session/providers/google';
import * as WebBrowser from 'expo-web-browser';
// Import client IDs and auth from your firebaseConfig
import {
  auth, // Firebase auth instance
  webClientId as importedWebClientId,
  androidClientId as importedAndroidClientId,
  // iosClientId as importedIosClientId, // Uncomment and import if you add iOS client ID
} from '../services/firebaseConfig'; // Ensure this path is correct
import { Platform } from 'react-native';

WebBrowser.maybeCompleteAuthSession();

interface AuthContextType {
  user: User | null;
  loading: boolean; // Represents overall auth state loading (initial check, sign-in process)
  isSigningIn: boolean; // Specifically for the sign-in action
  error: Error | null;
  signInWithGoogle: () => Promise<void>;
  signOut: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const [user, setUser] = useState<User | null>(null);
  const [initialLoading, setInitialLoading] = useState(true); // For the initial onAuthStateChanged check
  const [isSigningIn, setIsSigningIn] = useState(false); // For the Google sign-in process
  const [error, setError] = useState<Error | null>(null);

  // Configure Google Auth Request with all available client IDs
  // expo-auth-session will pick the correct one based on the platform.
  const [request, response, promptAsync] = Google.useIdTokenAuthRequest({
    webClientId: importedWebClientId,
    androidClientId: importedAndroidClientId,
    // iosClientId: importedIosClientId, // Add this when you have an iOS client ID
    selectAccount: Platform.OS === 'web', // selectAccount is often more relevant for web
  });

  // Effect to handle Google Sign-In response
  useEffect(() => {
    if (response) { // Check if response is not null
      setIsSigningIn(true); // Indicate that sign-in process is active due to response
      if (response.type === 'success') {
        const { id_token } = response.params;
        if (id_token && auth) { // Ensure auth is initialized
          const credential = GoogleAuthProvider.credential(id_token);
          signInWithCredential(auth, credential)
            .then(userCredential => {
              setUser(userCredential.user);
              setError(null);
            })
            .catch(authError => {
              console.error('Firebase sign-in error:', authError);
              setError(authError);
              setUser(null); // Ensure user is null on error
            })
            .finally(() => {
              setIsSigningIn(false);
            });
        } else {
          if (!id_token) setError(new Error('Google Sign-In did not return an ID token.'));
          if (!auth) setError(new Error('Firebase auth is not initialized.'));
          setIsSigningIn(false);
        }
      } else if (response.type === 'error') {
        console.error('Google Auth Session Error:', response.error);
        setError(response.error || new Error('Google Sign-In failed.'));
        setIsSigningIn(false);
      } else if (response.type === 'dismiss' || response.type === 'cancel') {
        console.log('Google Sign-In dismissed or cancelled by user.');
        // Optionally set an error or just reset loading state
        setError(null); // Clear previous errors if user cancelled
        setIsSigningIn(false);
      }
    }
  }, [response, auth]); // Add auth to dependency array as it's used inside

  // Effect for Firebase Auth state listener
  useEffect(() => {
    if (!auth) { // If Firebase auth isn't initialized, don't subscribe
        console.warn("Firebase auth is not initialized. Skipping onAuthStateChanged listener.");
        setInitialLoading(false);
        return;
    }
    setInitialLoading(true); // Start loading when setting up listener
    const unsubscribe = onAuthStateChanged(
      auth,
      currentUser => {
        setUser(currentUser);
        setError(null); // Clear any previous auth errors
        setInitialLoading(false);
      },
      authError => {
        console.error('Auth state error:', authError);
        setError(authError);
        setUser(null);
        setInitialLoading(false);
      }
    );
    return () => unsubscribe(); // Cleanup subscription
  }, [auth]); // Add auth to dependency array

  const signInWithGoogle = async () => {
    setError(null); // Clear previous errors
    
    // Check if the necessary client IDs are configured for the current platform
    // `request` will be null if `useIdTokenAuthRequest` fails due to missing required client IDs.
    if (!request) {
      let errorMessage = "Google Auth request is not initialized. ";
      if (Platform.OS === 'web' && !importedWebClientId) {
        errorMessage += "Web Client ID is missing. ";
      } else if (Platform.OS === 'android' && !importedAndroidClientId) {
        errorMessage += "Android Client ID is missing. ";
      }
      // Add iOS check if (Platform.OS === 'ios' && !importedIosClientId) ...
      errorMessage += "Check app.config.js/app.json 'extra' and services/firebaseConfig.js.";
      console.error(errorMessage);
      setError(new Error(errorMessage));
      return;
    }

    setIsSigningIn(true);
    try {
      await promptAsync();
      // The response will be handled by the useEffect hook listening to 'response'
      // setIsSigningIn will be set to false within that useEffect's finally block
    } catch (e: any) {
      console.error('Error during promptAsync:', e);
      setError(e);
      setIsSigningIn(false); // Ensure loading state is reset on direct error from promptAsync
    }
  };

  const signOutUser = async () => { // Renamed to avoid conflict with firebaseSignOut
    // setInitialLoading(true); // No, signOut is an action, not initial load
    setError(null);
    if (!auth) {
        console.error("Firebase auth is not initialized. Cannot sign out.");
        setError(new Error("Firebase auth is not initialized."));
        return;
    }
    try {
      await firebaseSignOut(auth);
      setUser(null); // User state is also updated by onAuthStateChanged, but good to be explicit
    } catch (e: any) {
      console.error('Sign out error:', e);
      setError(e);
    } finally {
      // setInitialLoading(false);
    }
  };

  return (
    <AuthContext.Provider
      value={{
        user,
        loading: initialLoading, // Overall loading reflects initial auth check
        isSigningIn,             // Specific loading for sign-in action
        error,
        signInWithGoogle,
        signOut: signOutUser,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};