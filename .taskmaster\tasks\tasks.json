{"master": {"tasks": [{"id": 11, "title": "Update Icon Constants for New Tabs", "description": "Update the icon mapping object in 'constants/icon.tsx' to align with the new tab structure. This involves removing the 'index' entry and adding a new 'create' entry mapped to the 'plus-square' Feather icon.", "details": "Modify the 'constants/icon.tsx' file. The 'icon' export object should be updated to include the 'create' key and remove the 'index' key. Use the '@expo/vector-icons' library for the Feather icons as specified.\n\nPseudo-code:\n// constants/icon.tsx\nimport { Feather } from '@expo/vector-icons';\n\nexport const icon = {\n  explore: (props) => <Feather name=\"search\" {...props} />,\n  create: (props) => <Feather name=\"plus-square\" {...props} />,\n  profile: (props) => <Feather name=\"user\" {...props} />,\n  // remove any old 'index' entry\n};", "testStrategy": "Verify the 'constants/icon.tsx' file exports an object with 'explore', 'create', and 'profile' keys. Check that the 'index' key has been removed. Ensure the correct Feather icons ('search', 'plus-square', 'user') are assigned.", "priority": "high", "dependencies": [], "status": "done", "subtasks": []}, {"id": 12, "title": "Create Placeholder 'Create' Screen File", "description": "Create a new placeholder screen file for the 'Create' tab to ensure the expo-router can resolve the route. This file will contain a minimal React Native component.", "details": "In the 'app/(tabs)/' directory, create a new file named 'create.tsx'. This file should export a default React Native component that renders a simple View with some placeholder text, like 'Create Screen'.\n\nPseudo-code:\n// app/(tabs)/create.tsx\nimport { Text, View } from 'react-native';\n\nexport default function CreateScreen() {\n  return (\n    <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>\n      <Text>Create Screen</Text>\n    </View>\n  );\n}", "testStrategy": "Confirm that the file 'app/(tabs)/create.tsx' exists. When the application is running, navigating to the 'create' route should render the placeholder component without any 'route not found' errors.", "priority": "high", "dependencies": [], "status": "pending", "subtasks": []}, {"id": 13, "title": "Update Tab Navigator Layout Configuration", "description": "Reconfigure the main tab navigator in 'app/(tabs)/_layout.tsx' to display the three required tabs: Explore, Create, and Profile. This involves removing the old 'index' screen and adding the new 'create' screen in the correct order.", "details": "Modify the 'TabLayout' component in 'app/(tabs)/_layout.tsx'. Use the 'Tabs' component from 'expo-router'. Define three 'Tabs.Screen' children for 'explore', 'create', and 'profile' in that specific order. Ensure the custom 'TabBar' component is passed to the 'tabBar' prop.\n\nPseudo-code:\n// app/(tabs)/_layout.tsx\nimport { TabBar } from '@/components/TabBar';\nimport { Tabs } from 'expo-router';\n\nconst TabLayout = () => (\n  <Tabs tabBar={props => <TabBar {...props} />}>\n    <Tabs.Screen name=\"explore\" options={{ title: \"Explore\" }} />\n    <Tabs.Screen name=\"create\" options={{ title: \"Create\" }} />\n    <Tabs.Screen name=\"profile\" options={{ title: \"Profile\" }} />\n  </Tabs>\n);\n\nexport default TabLayout;", "testStrategy": "Run the application and verify that the bottom tab bar displays three tabs in the order: Explore, Create, Profile. Tapping each tab should navigate to its corresponding screen without errors. The old 'index' tab should no longer be present.", "priority": "high", "dependencies": [12], "status": "done", "subtasks": []}, {"id": 14, "title": "Verify Custom TabBar Component Integration", "description": "Ensure the existing custom 'TabBar.tsx' and 'TabBarButton.tsx' components correctly render and handle the new three-tab configuration provided by the updated layout.", "details": "This task involves no new code but focuses on verification. The 'TabBar' component receives props from 'expo-router's Tabs component. The developer must trace these props (like 'state', 'descriptors', 'navigation') to ensure they are correctly interpreted by 'TabBar.tsx' to render three buttons. The icon mapping from 'constants/icon.tsx' should be correctly applied.", "testStrategy": "Launch the app and inspect the UI. Verify that three tab buttons are rendered using the custom components. Check that the correct icons ('search', 'plus-square', 'user') and initial labels ('Explore', 'Create', 'Profile') are displayed for each tab.", "priority": "medium", "dependencies": [11, 13], "status": "pending", "subtasks": []}, {"id": 15, "title": "Validate Active Tab Animation and State", "description": "Confirm that the active tab's visual state changes as per the requirements, utilizing the existing animation logic. This includes the sliding indicator, icon scaling, upward movement, and label fade-out.", "details": "Review the implementation within 'TabBar.tsx' and 'TabBarButton.tsx' that handles the 'isFocused' prop. The logic should trigger the necessary animations. For the sliding indicator, this likely involves using a library like 'react-native-reanimated' to animate the position of an 'Animated.View'. For the icon/label, it involves applying animated styles based on focus state.\n\nPseudo-code check in TabBarButton.tsx:\nconst isFocused = props.accessibilityState.selected;\n// Use isFocused to drive animations for scale, position, and opacity.", "testStrategy": "Manually test by tapping on each tab. The active tab should have a colored indicator underneath it. The indicator should slide smoothly between tabs. The active icon should scale up and move slightly upwards, while its text label fades out. All animations should be fluid.", "priority": "medium", "dependencies": [14], "status": "pending", "subtasks": []}, {"id": 16, "title": "Validate Inactive Tab Visual State", "description": "Confirm that inactive tabs are displayed correctly, showing both their icon and text label at the default size and without any background indicator.", "details": "Inspect the logic in 'TabBarButton.tsx' for the non-focused state (when 'isFocused' is false). Ensure that styles for inactive tabs correctly set the icon scale to its default value (e.g., 1), the label opacity to 1, and that no active indicator is rendered for them.", "testStrategy": "Observe the non-active tabs while interacting with the tab bar. Verify that they display both an icon and a text label. Confirm their size does not change and no colored indicator is present beneath them. This state should be consistent for any tab that is not currently selected.", "priority": "medium", "dependencies": [14], "status": "pending", "subtasks": []}, {"id": 17, "title": "End-to-End Navigation Flow Testing", "description": "Perform a comprehensive end-to-end test of the entire navigation flow to ensure all functional requirements are met and the user experience is seamless.", "details": "This is a manual testing task. The tester should perform the following actions repeatedly and in different sequences:\n1. Launch the app, observe the default active tab.\n2. Tap on 'Explore', 'Create', and 'Profile' tabs.\n3. Verify navigation to the correct screens.\n4. Verify all active/inactive state animations and visual changes work correctly on every tap.\n5. Test on different device screen sizes (if possible) to check for layout issues.", "testStrategy": "Create a test plan checklist covering all functional requirements (FR-1 to FR-4). Execute the plan and document any deviations from the expected behavior. The feature is considered validated when all checklist items pass without issues.", "priority": "medium", "dependencies": [13, 15, 16], "status": "pending", "subtasks": []}, {"id": 18, "title": "Animation Performance Profiling and Optimization", "description": "Profile the performance of the tab bar animations to ensure they meet the 60 FPS target on a range of devices, preventing any jank or lag in the UI.", "details": "Use performance profiling tools such as the React Native performance monitor, Flipper, or platform-specific tools (Xcode Instruments, Android Profiler). Focus on the JS thread and UI thread performance during tab transitions. Identify any long-running JavaScript functions or rendering bottlenecks that cause frames to drop. If issues are found, investigate optimizations, such as using 'useNativeDriver: true' for animations or memoizing components with 'React.memo'.", "testStrategy": "Run the app on a physical, mid-range target device. Enable the performance monitor and repeatedly switch between tabs. The UI thread FPS should consistently stay at or near 60 FPS. If it drops significantly, the test fails and optimization is required.", "priority": "low", "dependencies": [17], "status": "pending", "subtasks": []}], "metadata": {"created": "2025-06-30T09:42:47.084Z", "updated": "2025-07-01T17:56:31.910Z", "description": "Tasks for master context"}}}