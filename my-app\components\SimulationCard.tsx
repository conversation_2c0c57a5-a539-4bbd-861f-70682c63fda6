// File: components/SimulationCard.tsx

import React from 'react';
import { View, Text, StyleSheet, Image, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons'; // Or your preferred icon set
import { Link } from 'expo-router'; // To make the card navigable

// Define the structure of a simulation object that the card will accept
export interface Simulation {
  id: string; // For navigation
  title: string;
  description: string;
  authorName: string;
  // imageUri?: string; // Optional: if you have actual images
  iconPlaceholderColor?: string; // For the butterfly-like icon
  views: number | string; // Can be number or formatted string like "1.2k"
  forks: number;
  likes: number;
}

interface SimulationCardProps {
  simulation: Simulation;
  onPress?: () => void; // Optional: if direct onPress handling is needed instead of Link
}

const SimulationCard: React.FC<SimulationCardProps> = ({ simulation, onPress }) => {
  // Function to format large numbers (e.g., 1200 -> "1.2k")
  const formatStat = (num: number | string): string => {
    if (typeof num === 'string') return num; // Already formatted
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    }
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'k';
    }
    return num.toString();
  };

  const cardContent = (
    <View style={styles.cardContainer}>
      {/* Left side: Image/Icon Placeholder */}
      <View style={[styles.iconContainer, { backgroundColor: simulation.iconPlaceholderColor || '#007AFF' }]}>
        {/* You can replace this View with an <Image source={{ uri: simulation.imageUri }} /> if you have images */}
        {/* For the butterfly effect, you might use two overlapping and rotated Views or an SVG */}
        <View style={[styles.butterflyWing, styles.butterflyWingTop]} />
        <View style={[styles.butterflyWing, styles.butterflyWingBottom]} />
      </View>

      {/* Right side: Text content and stats */}
      <View style={styles.contentContainer}>
        <Text style={styles.title} numberOfLines={1}>{simulation.title}</Text>
        <Text style={styles.description} numberOfLines={2}>{simulation.description}</Text>

        <View style={styles.footer}>
          <Text style={styles.authorName}>{simulation.authorName}</Text>
          <View style={styles.statsContainer}>
            <Ionicons name="eye-outline" size={14} color={styles.statsText.color} />
            <Text style={styles.statsText}>{formatStat(simulation.views)}</Text>

            <Ionicons name="git-network-outline" size={14} color={styles.statsText.color} style={styles.statIcon} />
            {/* Using git-network for fork, or use a rotated git-commit like before */}
            {/* <Ionicons name="git-commit-outline" size={14} color={styles.statsText.color} style={[styles.statIcon, { transform: [{ rotate: '90deg' }]}]} /> */}
            <Text style={styles.statsText}>{simulation.forks}</Text>

            <Ionicons name="heart-outline" size={14} color={styles.statsText.color} style={styles.statIcon} />
            <Text style={styles.statsText}>{simulation.likes}</Text>
          </View>
        </View>
      </View>
    </View>
  );

  // Wrap with Link for navigation, or TouchableOpacity if onPress prop is used
  if (onPress) {
    return <TouchableOpacity onPress={onPress}>{cardContent}</TouchableOpacity>;
  }

  return (
    <Link
      href={{
        pathname: '/simulation/[id]', // The actual file path pattern for your dynamic route
        params: { id: simulation.id },   // The dynamic part as a parameter
      }}
      asChild
    >
      <TouchableOpacity>
        {cardContent}
      </TouchableOpacity>
    </Link>
  );
};

const styles = StyleSheet.create({
  cardContainer: {
    flexDirection: 'row',
    backgroundColor: '#2C2C2E', // Dark card background from screenshot
    borderRadius: 12,
    padding: 15,
    marginVertical: 8,
    marginHorizontal: 10, // Assuming some horizontal margin from the screen edges
    // Add shadow for depth, using modern boxShadow
    boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.3)', // offsetX offsetY blurRadius color
    // For native, elevation can be a fallback, but boxShadow is preferred for consistency
    // elevation: 3, // For Android shadow (less customizable)
  },
  iconContainer: {
    width: 50, // Adjust size as needed
    height: 50,
    borderRadius: 8,
    marginRight: 15,
    justifyContent: 'center',
    alignItems: 'center',
    // backgroundColor is dynamic via props
  },
  // Simple butterfly placeholder - for a real effect, consider SVG or multiple styled Views
  butterflyWing: {
    width: 12,
    height: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.7)', // Lighter part of the wing
    position: 'absolute',
  },
  butterflyWingTop: {
    transform: [{ rotate: '-30deg' }, { translateX: -5 }, { translateY: -3 }],
    borderTopLeftRadius: 10,
    borderBottomRightRadius: 10,
  },
  butterflyWingBottom: {
    transform: [{ rotate: '30deg' }, { translateX: 5 }, { translateY: 3 }],
    borderBottomLeftRadius: 10,
    borderTopRightRadius: 10,
  },
  contentContainer: {
    flex: 1, // Takes remaining width
    justifyContent: 'space-between', // Distributes space between title/desc and footer
  },
  title: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  description: {
    color: '#E0E0E0', // Lighter than title, but still bright
    fontSize: 13,
    marginBottom: 8,
    lineHeight: 18,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 'auto', // Pushes footer to the bottom of contentContainer
  },
  authorName: {
    color: '#B0B0B0', // Subdued author text color
    fontSize: 12,
  },
  statsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statIcon: {
    marginLeft: 8, // Space before new stat icon
  },
  statsText: {
    color: '#B0B0B0', // Same as author for consistency in footer
    fontSize: 12,
    marginLeft: 3, // Space between icon and text
  },
});

export default SimulationCard;