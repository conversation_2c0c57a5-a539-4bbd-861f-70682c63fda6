// File: services/firestoreService.ts

import {
    collection,
    query,
    orderBy,
    limit as firestoreLimit, // Renamed to avoid conflict with JavaScript's limit
    startAfter,
    getDocs,
    doc,
    getDoc,
    DocumentData,
    QueryDocumentSnapshot,
    Timestamp, // Import Timestamp for type safety
    setDoc,
    serverTimestamp
  } from 'firebase/firestore';
  import { firestore } from './firebaseConfig'; // Your initialized Firebase app
  
  const SIMULATIONS_COLLECTION = 'items';
  const USERS_COLLECTION = 'users';

// Definition of SimulationDocument (assuming it's in this file or imported)
export interface SimulationDocument {
  id?: string;
  parentId: string;
  title: string;
  description: string;
  content: string;
  theme: string;
  type: '2D' | '3D';
  likeCount: number;
  creatorId: string;
  userName: string;
  userPhotoUrl?: string | null;
  tags: string[];
  simTokens: number;
  themeTokens: number;
  createdAt: Timestamp;
  updatedAt: Timestamp;
  viewCount?: number; // Added based on previous discussion
  forkCount?: number; // Added based on previous discussion
}

/**
 * Fetches a paginated list of simulations from Firestore.
 * @param count The number of simulations to fetch.
 * @param lastFetchedDoc The last document snapshot from the previous fetch, for pagination.
 * @returns A promise that resolves to an object containing the list of simulations and the last fetched document.
 */
export const fetchSimulations = async (
  count: number,
  lastFetchedDoc?: QueryDocumentSnapshot<DocumentData>
): Promise<{ simulations: SimulationDocument[]; lastVisibleDoc: QueryDocumentSnapshot<DocumentData> | null }> => {
  // Check if Firestore is initialized
  if (!firestore) {
    console.error("Firestore is not initialized. Cannot fetch simulations.");
    // Return an empty state or throw a specific error
    return { simulations: [], lastVisibleDoc: null };
  }

  try {
    const simulationsColRef = collection(firestore, SIMULATIONS_COLLECTION); // Use the imported firestore instance
    let q;

    if (lastFetchedDoc) {
      q = query(
        simulationsColRef,
        orderBy('createdAt', 'desc'),
        startAfter(lastFetchedDoc),
        firestoreLimit(count)
      );
    } else {
      q = query(
        simulationsColRef,
        orderBy('createdAt', 'desc'),
        firestoreLimit(count)
      );
    }

    const querySnapshot = await getDocs(q);
    const simulations: SimulationDocument[] = [];
    querySnapshot.forEach((docSnapshot) => {
      // Ensure data conversion is safe, especially for Timestamps
      const data = docSnapshot.data();
      simulations.push({
        id: docSnapshot.id,
        parentId: data.parentId,
        title: data.title,
        description: data.description,
        content: data.content,
        theme: data.theme,
        type: data.type,
        likeCount: data.likeCount,
        creatorId: data.creatorId,
        userName: data.userName,
        userPhotoUrl: data.userPhotoUrl,
        tags: data.tags || [], // Default to empty array if tags are missing
        simTokens: data.simTokens,
        themeTokens: data.themeTokens,
        createdAt: data.createdAt as Timestamp, // Assert type if confident, or validate
        updatedAt: data.updatedAt as Timestamp, // Assert type if confident, or validate
        viewCount: data.viewCount,
        forkCount: data.forkCount,
        // Add other fields explicitly if needed for type safety
      });
    });

    const lastVisibleDoc = querySnapshot.docs[querySnapshot.docs.length - 1] || null;

    return { simulations, lastVisibleDoc };
  } catch (error) {
    console.error("Error fetching simulations:", error);
    throw error; // Re-throw to be handled by the caller (e.g., in useAuth or component)
  }
};

/**
 * Fetches a single simulation by its ID from Firestore.
 * @param id The ID of the simulation to fetch.
 * @returns A promise that resolves to the simulation document or null if not found.
 */
export const fetchSimulationById = async (id: string): Promise<SimulationDocument | null> => {
  // Check if Firestore is initialized
  if (!firestore) {
    console.error("Firestore is not initialized. Cannot fetch simulation by ID.");
    return null; // Or throw an error
  }

  if (!id) {
    console.error("Error fetching simulation: ID is undefined or empty.");
    return null;
  }

  try {
    const docRef = doc(firestore, SIMULATIONS_COLLECTION, id); // Use the imported firestore instance
    const docSnap = await getDoc(docRef);

    if (docSnap.exists()) {
      const data = docSnap.data();
      return {
        id: docSnap.id,
        parentId: data.parentId,
        title: data.title,
        description: data.description,
        content: data.content,
        theme: data.theme,
        type: data.type,
        likeCount: data.likeCount,
        creatorId: data.creatorId,
        userName: data.userName,
        userPhotoUrl: data.userPhotoUrl,
        tags: data.tags || [],
        simTokens: data.simTokens,
        themeTokens: data.themeTokens,
        createdAt: data.createdAt as Timestamp,
        updatedAt: data.updatedAt as Timestamp,
        viewCount: data.viewCount,
        forkCount: data.forkCount,
      };
    } else {
      console.log(`No simulation found with ID: ${id}`);
      return null;
    }
  } catch (error) {
    console.error(`Error fetching simulation with ID ${id}:`, error);
    throw error; // Re-throw to be handled by the caller
  }
};
  
  // --- Potentially other functions you might need ---
  
  /**
   * Creates a new simulation document in Firestore.
   * (Example - you'll need to adapt based on your "Create Screen" fields)
   */
  // export const createSimulation = async (simulationData: Omit<SimulationDocument, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> => {
  //   try {
  //     const simulationsColRef = collection(db, SIMULATIONS_COLLECTION);
  //     const newDocRef = await addDoc(simulationsColRef, {
  //       ...simulationData,
  //       createdAt: serverTimestamp(), // Use serverTimestamp for creation
  //       updatedAt: serverTimestamp(), // and initial update
  //     });
  //     return newDocRef.id;
  //   } catch (error) {
  //     console.error("Error creating simulation:", error);
  //     throw error;
  //   }
  // };
  
  /**
   * Updates an existing simulation document.
   */
  // export const updateSimulation = async (id: string, updates: Partial<SimulationDocument>): Promise<void> => {
  //   try {
  //     const docRef = doc(db, SIMULATIONS_COLLECTION, id);
  //     await updateDoc(docRef, {
  //       ...updates,
  //       updatedAt: serverTimestamp(), // Always update the updatedAt timestamp
  //     });
  //   } catch (error) {
  //     console.error(`Error updating simulation ${id}:`, error);
  //     throw error;
  //   }
  // };
  
  /**
   * Increments the like count for a simulation.
   * (This is an example of a specific update using FieldValue.increment)
   */
  // import { updateDoc, FieldValue, increment } from 'firebase/firestore'; // FieldValue and increment
  // export const likeSimulation = async (id: string): Promise<void> => {
  //   try {
  //     const docRef = doc(db, SIMULATIONS_COLLECTION, id);
  //     await updateDoc(docRef, {
  //       likeCount: increment(1), // Atomically increment the likeCount
  //       updatedAt: serverTimestamp(),
  //     });
  //   } catch (error) {
  //     console.error(`Error liking simulation ${id}:`, error);
  //     throw error;
  //   }
  // };

  export interface UserProfileDocument {
    uid: string; // Should match Auth UID
    displayName?: string | null; // Denormalized, can be updated here too
    email?: string | null; // Denormalized
    bio?: string;
    photoURL?: string | null; // Denormalized
    createdAt?: Timestamp;
    updatedAt?: Timestamp;
    // Add any other profile fields you want to store
  }
  
  /**
   * Fetches a user's profile from Firestore.
   * @param uid The user's ID.
   * @returns A promise that resolves to the user profile document or null if not found.
   */
  export const fetchUserProfile = async (uid: string): Promise<UserProfileDocument | null> => {
    if (!firestore) {
      console.error("Firestore is not initialized. Cannot fetch user profile.");
      return null;
    }
    if (!uid) {
      console.warn("UID is undefined, cannot fetch user profile.");
      return null;
    }
  
    try {
      const docRef = doc(firestore, USERS_COLLECTION, uid);
      const docSnap = await getDoc(docRef);
  
      if (docSnap.exists()) {
        return { uid, ...docSnap.data() } as UserProfileDocument;
      } else {
        console.log(`No user profile found for UID: ${uid}`);
        return null; // Or you might want to return a default profile structure
      }
    } catch (error) {
      console.error(`Error fetching user profile for UID ${uid}:`, error);
      throw error;
    }
  };
  
  /**
   * Updates or creates a user's profile in Firestore.
   * @param uid The user's ID.
   * @param profileData The partial data to update/set.
   * @returns A promise that resolves when the operation is complete.
   */
  export const updateUserProfile = async (uid: string, profileData: Partial<Omit<UserProfileDocument, 'uid' | 'createdAt'>>) => {
    if (!firestore) {
      console.error("Firestore is not initialized. Cannot update user profile.");
      throw new Error("Firestore not initialized");
    }
    if (!uid) {
      console.warn("UID is undefined, cannot update user profile.");
      throw new Error("User ID is required to update profile");
    }
  
    try {
      const docRef = doc(firestore, USERS_COLLECTION, uid);
      // Use setDoc with merge: true to create if not exists, or update if it does.
      // Include updatedAt server timestamp.
      // If creating for the first time, also set createdAt.
      const dataToSet = {
          ...profileData,
          updatedAt: serverTimestamp(),
      };
      // Check if document exists to conditionally add createdAt
      const currentProfile = await getDoc(docRef);
      if (!currentProfile.exists()) {
          (dataToSet as any).createdAt = serverTimestamp();
      }
  
      await setDoc(docRef, dataToSet, { merge: true });
      console.log(`User profile for UID ${uid} updated successfully.`);
    } catch (error) {
      console.error(`Error updating user profile for UID ${uid}:`, error);
      throw error;
    }
  };