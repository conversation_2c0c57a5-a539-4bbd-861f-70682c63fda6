// File: app/(tabs)/index.tsx

import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  FlatList,
  ActivityIndicator,
  RefreshControl, // For pull-to-refresh
} from 'react-native';
import { Ionicons, MaterialCommunityIcons } from '@expo/vector-icons';
// Import your service and card component
import { fetchSimulations, SimulationDocument } from '../../services/firestoreService'; // Adjust path
import SimulationCard, { Simulation as SimulationCardData } from '../../components/SimulationCard'; // Adjust path
import { QueryDocumentSnapshot, DocumentData } from 'firebase/firestore';

const HEADER_BACKGROUND_COLOR = '#1C1C1E';
const SIMULATIONS_PER_PAGE = 7; // Adjust page size as needed

export default function ExploreScreen() {
  const [simulations, setSimulations] = useState<SimulationDocument[]>([]);
  const [loadingInitial, setLoadingInitial] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [lastDoc, setLastDoc] = useState<QueryDocumentSnapshot<DocumentData> | undefined>(undefined);
  const [hasMoreData, setHasMoreData] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Placeholder action handlers for header icons
  const handleFilterPress = () => console.log('Filter pressed');
  const handleSearchPress = () => console.log('Search pressed');
  const handleExploreTitlePress = () => console.log('Explore title pressed (for dropdown)');

  const loadSimulations = useCallback(async (isRefresh = false) => {
    if (loadingMore && !isRefresh) return; // Prevent multiple simultaneous loads for "load more"
    if (!hasMoreData && !isRefresh) return; // No more data to fetch unless refreshing

    setError(null);
    if (isRefresh) {
      setRefreshing(true);
      setLastDoc(undefined); // Reset pagination for refresh
      setSimulations([]); // Clear current simulations for refresh
      setHasMoreData(true); // Assume there's data when refreshing
    } else if (!lastDoc) { // Initial load (not a refresh, no lastDoc)
      setLoadingInitial(true);
    } else { // Loading more
      setLoadingMore(true);
    }

    try {
      const { simulations: newSimulations, lastVisibleDoc } = await fetchSimulations(
        SIMULATIONS_PER_PAGE,
        isRefresh ? undefined : lastDoc // Pass lastDoc for pagination, undefined for refresh/initial
      );

      if (newSimulations.length > 0) {
        setSimulations(prevSimulations =>
          isRefresh ? newSimulations : [...prevSimulations, ...newSimulations]
        );
        setLastDoc(lastVisibleDoc || undefined);
      }

      if (newSimulations.length < SIMULATIONS_PER_PAGE || !lastVisibleDoc) {
        setHasMoreData(false); // No more documents if fewer than page size returned or no lastVisibleDoc
      }
    } catch (e: any) {
      console.error("Failed to load simulations:", e);
      setError(e.message || "An error occurred while fetching simulations.");
    } finally {
      if (isRefresh) {
        setRefreshing(false);
      } else if (!lastDoc && !isRefresh) { // End of initial load
        setLoadingInitial(false);
      } else { // End of loading more
        setLoadingMore(false);
      }
    }
  }, [lastDoc, hasMoreData, loadingMore]); // Dependencies for useCallback

  // Initial data load
  useEffect(() => {
    loadSimulations(true); // Load initially as if it's a refresh to set everything up
  }, []); // Empty dependency array: run once on mount

  const onRefresh = useCallback(() => {
    loadSimulations(true);
  }, [loadSimulations]); // loadSimulations is already memoized

  const handleLoadMore = () => {
    if (!loadingMore && hasMoreData && !loadingInitial && !refreshing) { // Check states to prevent multiple calls
      loadSimulations();
    }
  };

  // Map Firestore SimulationDocument to what SimulationCard expects
  const mapToCardData = (doc: SimulationDocument): SimulationCardData => ({
    id: doc.id || '', // Ensure id is always a string
    title: doc.title,
    description: doc.description,
    authorName: doc.userName,
    views: doc.viewCount !== undefined ? doc.viewCount : "N/A", // Use viewCount if available
    forks: doc.forkCount !== undefined ? doc.forkCount : 0,   // Use forkCount if available
    likes: doc.likeCount,
    iconPlaceholderColor: '#3498db', // Example color, or derive from doc.theme or type
  });

  const renderItem = ({ item }: { item: SimulationDocument }) => (
    <SimulationCard simulation={mapToCardData(item)} />
  );

  const ListEmptyComponent = () => {
    if (loadingInitial || refreshing) return null; // Don't show empty message while initially loading or refreshing
    if (error) {
      return (
        <View style={styles.centeredMessageContainer}>
          <Text style={styles.errorText}>{error}</Text>
          <TouchableOpacity onPress={onRefresh} style={styles.retryButton}>
            <Text style={styles.retryButtonText}>Try Again</Text>
          </TouchableOpacity>
        </View>
      );
    }
    return (
      <View style={styles.centeredMessageContainer}>
        <Text style={styles.infoText}>No simulations found. Start exploring or create your own!</Text>
      </View>
    );
  };

  const ListFooterComponent = () => {
    if (loadingMore) {
      return <ActivityIndicator size="small" color="#FFFFFF" style={{ marginVertical: 20 }} />;
    }
    // Optionally show a "No more simulations" message if !hasMoreData and simulations.length > 0
    // if (!hasMoreData && simulations.length > 0 && !loadingInitial && !refreshing) {
    //   return <Text style={styles.infoTextFooter}>You've reached the end!</Text>;
    // }
    return null;
  };


  return (
    <SafeAreaView style={styles.safeArea}>
      <StatusBar barStyle="light-content" backgroundColor={HEADER_BACKGROUND_COLOR} />
      {/* Custom Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={handleExploreTitlePress} style={styles.headerTitleContainer}>
          <Text style={styles.headerTitle}>Explore</Text>
        </TouchableOpacity>
        <View style={styles.headerIconsContainer}>
          <TouchableOpacity onPress={handleFilterPress} style={styles.iconButton}>
            <MaterialCommunityIcons name="filter-variant" size={24} color="#FFFFFF" />
          </TouchableOpacity>
          <TouchableOpacity onPress={handleSearchPress} style={styles.iconButton}>
            <Ionicons name="search" size={24} color="#FFFFFF" />
          </TouchableOpacity>
        </View>
      </View>

      {/* Main Content Area - FlatList for simulations */}
      {loadingInitial && simulations.length === 0 && !error ? (
        <View style={styles.centeredMessageContainer}>
          <ActivityIndicator size="large" color="#FFFFFF" />
          <Text style={styles.loadingText}>Loading simulations...</Text>
        </View>
      ) : (
        <FlatList
          data={simulations}
          renderItem={renderItem}
          keyExtractor={(item) => item.id || Math.random().toString()} // Ensure unique string key
          contentContainerStyle={styles.listContentContainer}
          onEndReached={handleLoadMore}
          onEndReachedThreshold={0.5} // How far from the end (0 to 1) to trigger onEndReached
          ListEmptyComponent={ListEmptyComponent}
          ListFooterComponent={ListFooterComponent}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              tintColor="#FFFFFF" // For iOS
              colors={["#FFFFFF", "#5A67D8"]} // For Android
              progressBackgroundColor="#1C1C1E" // For Android
            />
          }
        />
      )}
    </SafeAreaView>
  );
}

// Update your styles
const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: HEADER_BACKGROUND_COLOR,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 15,
    paddingVertical: 12,
    backgroundColor: HEADER_BACKGROUND_COLOR,
    borderBottomWidth: 1,
    borderBottomColor: '#3A3A3C',
  },
  headerTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerTitle: {
    color: '#FFFFFF',
    fontSize: 22,
    fontWeight: 'bold',
  },
  dropdownIcon: {
    marginLeft: 5,
  },
  headerIconsContainer: {
    flexDirection: 'row',
  },
  iconButton: {
    padding: 8,
    marginLeft: 10,
  },
  listContentContainer: {
    paddingBottom: 20, // Space at the bottom of the list
    // If your cards have horizontal margin, FlatList itself shouldn't have horizontal padding
    // paddingHorizontal: 10, // Removed, as SimulationCard has marginHorizontal
  },
  centeredMessageContainer: {
    flex: 1, // Take up available space if list is empty
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#121212', // Match list container background
  },
  loadingText: {
    marginTop: 10,
    color: '#A0A0A0',
    fontSize: 16,
  },
  infoText: {
    color: '#A0A0A0',
    fontSize: 16,
    textAlign: 'center',
  },
  // infoTextFooter: {
  //   color: '#A0A0A0',
  //   fontSize: 14,
  //   textAlign: 'center',
  //   paddingVertical: 15,
  // },
  errorText: {
    color: '#FF6B6B', // A suitable error color
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 15,
  },
  retryButton: {
    backgroundColor: '#5A67D8',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  retryButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  // Remove mock item styles if they are no longer needed directly in this file
  // mockItemContainer: { ... },
  // mockItemImagePlaceholder: { ... },
  // ...
});